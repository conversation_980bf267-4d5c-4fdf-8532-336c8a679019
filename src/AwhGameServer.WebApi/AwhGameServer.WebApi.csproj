<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <PreserveCompilationContext>true</PreserveCompilationContext>
        <CopyOutputSymbolsToPublishDirectory>true</CopyOutputSymbolsToPublishDirectory>
        <CopyOutputSymbolsToBuildDirectory>true</CopyOutputSymbolsToBuildDirectory>
    </PropertyGroup>

    <ItemGroup>
      <Content Include="..\..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.7" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\AwhGameServer.Application\AwhGameServer.Application.csproj" />
      <ProjectReference Include="..\AwhGameServer.Contracts\AwhGameServer.Contracts.csproj" />
      <ProjectReference Include="..\AwhGameServer.Infrastructure\AwhGameServer.Infrastructure.csproj" />
    </ItemGroup>

</Project>
