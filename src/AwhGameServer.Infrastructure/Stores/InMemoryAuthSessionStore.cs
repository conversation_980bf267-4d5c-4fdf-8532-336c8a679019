using AwhGameServer.Application.Abstractions.Models;
using AwhGameServer.Application.Abstractions.Stores;
using AwhGameServer.Domain.ValueObjects.Users;

namespace AwhGameServer.Infrastructure.Stores;

/// <summary>
/// Реализация хранилища сессий аутентификации в памяти.
/// Предназначена для разработки, тестирования и демонстрационных целей.
/// </summary>
/// <remarks>
/// Данная реализация хранит все сессии в памяти приложения и не обеспечивает персистентность данных.
/// При перезапуске приложения все сессии будут потеряны.
/// Автоматически удаляет истекшие сессии при обращении к ним.
/// Не рекомендуется для использования в продакшене из-за отсутствия персистентности и ограничений масштабируемости.
/// </remarks>
public class InMemoryAuthSessionStore : IAuthSessionStore
{
    /// <summary>
    /// Коллекция активных сессий аутентификации в памяти.
    /// </summary>
    private readonly List<AuthSession> _sessions = [];

    /// <summary>
    /// Получает сессию аутентификации по идентификатору сессии.
    /// </summary>
    /// <param name="sessionId">Уникальный идентификатор сессии.</param>
    /// <param name="cancellationToken">Токен отмены операции.</param>
    /// <returns>
    /// Сессию аутентификации, если найдена и не истекла, иначе <see langword="null"/>.
    /// </returns>
    /// <remarks>
    /// Автоматически удаляет сессию из хранилища, если она истекла.
    /// </remarks>
    public Task<AuthSession?> GetSessionAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        var session = _sessions.FirstOrDefault(x => x.SessionId == sessionId);
        
        if (session is not null && HandleSessionExpiration(session))
            session = null;
        
        return Task.FromResult(session);
    }

    /// <summary>
    /// Получает сессию аутентификации по хешу refresh токена.
    /// </summary>
    /// <param name="refreshTokenHash">Хеш refresh токена для поиска сессии.</param>
    /// <param name="cancellationToken">Токен отмены операции.</param>
    /// <returns>
    /// Сессию аутентификации, если найдена и не истекла, иначе <see langword="null"/>.
    /// </returns>
    /// <remarks>
    /// Автоматически удаляет сессию из хранилища, если она истекла.
    /// </remarks>
    public Task<AuthSession?> GetSessionByRefreshHashAsync(TokenHash refreshTokenHash, CancellationToken cancellationToken = default)
    {
        var session = _sessions.FirstOrDefault(x => x.RefreshHash == refreshTokenHash.HashBase64Url);
        
        if (session is not null && HandleSessionExpiration(session))
            session = null;
        
        return Task.FromResult(session);
    }

    /// <summary>
    /// Атомарно отзывает все существующие сессии пользователя и создает новую сессию.
    /// </summary>
    /// <param name="sessionId">Уникальный идентификатор новой сессии.</param>
    /// <param name="userId">Идентификатор пользователя.</param>
    /// <param name="refreshTokenHash">Хеш refresh токена для новой сессии.</param>
    /// <param name="authIdentityId">Идентификатор метода аутентификации.</param>
    /// <param name="expiresAtUtc">Время истечения новой сессии в UTC.</param>
    /// <param name="cancellationToken">Токен отмены операции.</param>
    /// <remarks>
    /// Операция выполняется атомарно: сначала удаляются все существующие сессии пользователя,
    /// затем создается новая сессия. Если существует коллизия по sessionId, старая сессия
    /// с таким же идентификатором будет удалена при истечении срока действия.
    /// </remarks>
    public Task RevokeAllUserSessionsThenCreateAsync(string sessionId, UserId userId, TokenHash refreshTokenHash,
        AuthIdentityId authIdentityId, DateTime expiresAtUtc, CancellationToken cancellationToken = default)
    {
        _sessions.RemoveAll(x => x.UserId == userId);

        var session = new AuthSession(
            sessionId,
            userId,
            refreshTokenHash.HashBase64Url,
            authIdentityId,
            false,
            null,
            DateTime.UtcNow,
            expiresAtUtc);
        
        var collidingSession = _sessions.FirstOrDefault(x => x.SessionId == sessionId);

        if (collidingSession is not null)
        {
            _sessions.Remove(collidingSession);
        }

        _sessions.Add(session);
        
        return Task.CompletedTask;
    }

    /// <summary>
    /// Пытается обновить refresh токен в существующей сессии.
    /// </summary>
    /// <param name="expectedOldRefreshTokenHash">Ожидаемый хеш текущего refresh токена.</param>
    /// <param name="newRefreshTokenHash">Новый хеш refresh токена.</param>
    /// <param name="newExpiresAtUtc">Новое время истечения сессии.</param>
    /// <param name="cancellationToken">Токен отмены операции.</param>
    /// <returns>
    /// <see langword="true"/>, если сессия найдена и успешно обновлена;
    /// <see langword="false"/>, если сессия не найдена или истекла.
    /// </returns>
    /// <remarks>
    /// Операция выполняется атомарно: проверяется существование сессии с указанным хешем,
    /// затем обновляется хеш refresh токена и время истечения.
    /// Автоматически удаляет сессию, если она истекла.
    /// </remarks>
    public Task<bool> TryRotateRefreshAsync(TokenHash expectedOldRefreshTokenHash, TokenHash newRefreshTokenHash,
        DateTimeOffset newExpiresAtUtc, CancellationToken cancellationToken = default)
    {
        var session = _sessions.FirstOrDefault(x => x.RefreshHash == expectedOldRefreshTokenHash.HashBase64Url);
        
        if (session is null || HandleSessionExpiration(session)) 
            return Task.FromResult(false);

        var index = _sessions.IndexOf(session);
        
        session = session with { RefreshHash = newRefreshTokenHash.HashBase64Url, ExpiresAtUtc = newExpiresAtUtc.UtcDateTime };
        
        _sessions[index] = session;
        
        return Task.FromResult(true);
    }

    /// <summary>
    /// Обрабатывает истечение срока действия сессии.
    /// </summary>
    /// <param name="session">Сессия для проверки на истечение.</param>
    /// <returns>
    /// <see langword="true"/>, если сессия истекла и была удалена из хранилища;
    /// <see langword="false"/>, если сессия не истекла или равна <see langword="null"/>.
    /// </returns>
    /// <remarks>
    /// Если сессия истекла (время истечения меньше текущего времени UTC),
    /// она автоматически удаляется из коллекции сессий.
    /// </remarks>
    private bool HandleSessionExpiration(AuthSession? session)
    {
        if (session is null) return false;
        
        if (session.ExpiresAtUtc >= DateTime.UtcNow) return false;
        
        _sessions.Remove(session);
            
        return true;
    }
}
