using AwhGameServer.Application.Abstractions.Models;
using AwhGameServer.Application.Abstractions.Stores;
using AwhGameServer.Domain.ValueObjects.Users;

namespace AwhGameServer.Infrastructure.Stores;

public class InMemoryAuthSessionStore : IAuthSessionStore
{
    private readonly List<AuthSession> _sessions = [];
    
    public Task<AuthSession?> GetSessionAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        var session = _sessions.FirstOrDefault(x => x.SessionId == sessionId);
        
        if (session is not null && HandleSessionExpiration(session))
            session = null;
        
        return Task.FromResult(session);
    }

    public Task<AuthSession?> GetSessionByRefreshHashAsync(TokenHash refreshTokenHash, CancellationToken cancellationToken = default)
    {
        var session = _sessions.FirstOrDefault(x => x.RefreshHash == refreshTokenHash.HashBase64Url);
        
        if (session is not null && HandleSessionExpiration(session))
            session = null;
        
        return Task.FromResult(session);
    }

    public Task RevokeAllUserSessionsThenCreateAsync(string sessionId, UserId userId, TokenHash refreshTokenHash,
        AuthIdentityId authIdentityId, DateTime expiresAtUtc, CancellationToken cancellationToken = default)
    {
        _sessions.RemoveAll(x => x.UserId == userId);

        var session = new AuthSession(
            sessionId,
            userId,
            refreshTokenHash.HashBase64Url,
            authIdentityId,
            false,
            null,
            DateTime.UtcNow,
            expiresAtUtc);
        
        var collidingSession = _sessions.FirstOrDefault(x => x.SessionId == sessionId);
        
        if (collidingSession is not null && HandleSessionExpiration(session))
            collidingSession = null;
        
        _sessions.Add(session);
        
        return Task.CompletedTask;
    }

    public Task<bool> TryRotateRefreshAsync(TokenHash expectedOldRefreshTokenHash, TokenHash newRefreshTokenHash,
        DateTimeOffset newExpiresAtUtc, CancellationToken cancellationToken = default)
    {
        var session = _sessions.FirstOrDefault(x => x.RefreshHash == expectedOldRefreshTokenHash.HashBase64Url);
        
        if (session is null || HandleSessionExpiration(session)) 
            return Task.FromResult(false);

        var index = _sessions.IndexOf(session);
        
        session = session with { RefreshHash = newRefreshTokenHash.HashBase64Url, ExpiresAtUtc = newExpiresAtUtc.UtcDateTime };
        
        _sessions[index] = session;
        
        return Task.FromResult(true);
    }
    
    private bool HandleSessionExpiration(AuthSession? session)
    {
        if (session is null) return false;
        
        if (session.ExpiresAtUtc >= DateTime.UtcNow) return false;
        
        _sessions.Remove(session);
            
        return true;
    }
}
