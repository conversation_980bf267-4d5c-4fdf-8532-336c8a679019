using AwhGameServer.Application.Abstractions.Models;
using AwhGameServer.Domain.ValueObjects.Users;
using AwhGameServer.Infrastructure.Stores;
using FluentAssertions;

namespace AwhGameServer.Infrastructure.Tests.Stores;

/// <summary>
/// Тесты для InMemoryAuthSessionStore - реализации хранилища сессий аутентификации в памяти.
/// Проверяют корректность операций создания, получения, обновления сессий и обработки истечения срока действия.
/// </summary>
public class InMemoryAuthSessionStoreTests
{
    private readonly InMemoryAuthSessionStore _store;

    public InMemoryAuthSessionStoreTests()
    {
        _store = new InMemoryAuthSessionStore();
    }

    #region GetSessionAsync Tests

    [Fact(DisplayName = "GetSessionAsync с валидным sessionId возвращает существующую сессию")]
    public async Task GetSessionAsync_WithValidSessionId_ReturnsExistingSession()
    {
        // Arrange
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");
        var refreshHash = new TokenHash("refresh-hash-789");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);

        await _store.RevokeAllUserSessionsThenCreateAsync(sessionId, userId, refreshHash, authIdentityId, expiresAt);

        // Act
        var result = await _store.GetSessionAsync(sessionId);

        // Assert
        result.Should().NotBeNull();
        result!.SessionId.Should().Be(sessionId);
        result.UserId.Should().Be(userId);
        result.RefreshHash.Should().Be(refreshHash.HashBase64Url);
        result.AuthIdentityId.Should().Be(authIdentityId);
        result.ExpiresAtUtc.Should().Be(expiresAt);
        result.IsRevoked.Should().BeFalse();
        result.RevokedAtUtc.Should().BeNull();
    }

    [Fact(DisplayName = "GetSessionAsync с несуществующим sessionId возвращает null")]
    public async Task GetSessionAsync_WithNonExistentSessionId_ReturnsNull()
    {
        // Act
        var result = await _store.GetSessionAsync("non-existent-session");

        // Assert
        result.Should().BeNull();
    }

    [Fact(DisplayName = "GetSessionAsync с истекшей сессией возвращает null и удаляет сессию")]
    public async Task GetSessionAsync_WithExpiredSession_ReturnsNullAndRemovesSession()
    {
        // Arrange
        var sessionId = "expired-session-123";
        var userId = new UserId("user-456");
        var refreshHash = new TokenHash("refresh-hash-789");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiredTime = DateTime.UtcNow.AddHours(-1); // Истекшая сессия

        await _store.RevokeAllUserSessionsThenCreateAsync(sessionId, userId, refreshHash, authIdentityId, expiredTime);

        // Act
        var result = await _store.GetSessionAsync(sessionId);

        // Assert
        result.Should().BeNull();

        // Проверяем, что сессия была удалена - повторный вызов также должен вернуть null
        var secondResult = await _store.GetSessionAsync(sessionId);
        secondResult.Should().BeNull();
    }

    #endregion

    #region GetSessionByRefreshHashAsync Tests

    [Fact(DisplayName = "GetSessionByRefreshHashAsync с валидным хешем возвращает существующую сессию")]
    public async Task GetSessionByRefreshHashAsync_WithValidHash_ReturnsExistingSession()
    {
        // Arrange
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");
        var refreshHash = new TokenHash("refresh-hash-789");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);

        await _store.RevokeAllUserSessionsThenCreateAsync(sessionId, userId, refreshHash, authIdentityId, expiresAt);

        // Act
        var result = await _store.GetSessionByRefreshHashAsync(refreshHash);

        // Assert
        result.Should().NotBeNull();
        result!.SessionId.Should().Be(sessionId);
        result.UserId.Should().Be(userId);
        result.RefreshHash.Should().Be(refreshHash.HashBase64Url);
        result.AuthIdentityId.Should().Be(authIdentityId);
    }

    [Fact(DisplayName = "GetSessionByRefreshHashAsync с несуществующим хешем возвращает null")]
    public async Task GetSessionByRefreshHashAsync_WithNonExistentHash_ReturnsNull()
    {
        // Act
        var result = await _store.GetSessionByRefreshHashAsync(new TokenHash("non-existent-hash"));

        // Assert
        result.Should().BeNull();
    }

    [Fact(DisplayName = "GetSessionByRefreshHashAsync с истекшей сессией возвращает null и удаляет сессию")]
    public async Task GetSessionByRefreshHashAsync_WithExpiredSession_ReturnsNullAndRemovesSession()
    {
        // Arrange
        var sessionId = "expired-session-123";
        var userId = new UserId("user-456");
        var refreshHash = new TokenHash("refresh-hash-789");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiredTime = DateTime.UtcNow.AddHours(-1);

        await _store.RevokeAllUserSessionsThenCreateAsync(sessionId, userId, refreshHash, authIdentityId, expiredTime);

        // Act
        var result = await _store.GetSessionByRefreshHashAsync(refreshHash);

        // Assert
        result.Should().BeNull();

        // Проверяем, что сессия была удалена
        var secondResult = await _store.GetSessionByRefreshHashAsync(refreshHash);
        secondResult.Should().BeNull();
    }

    #endregion

    #region RevokeAllUserSessionsThenCreateAsync Tests

    [Fact(DisplayName = "RevokeAllUserSessionsThenCreateAsync создает новую сессию с корректными параметрами")]
    public async Task RevokeAllUserSessionsThenCreateAsync_CreatesNewSessionWithCorrectParameters()
    {
        // Arrange
        var sessionId = "new-session-123";
        var userId = new UserId("user-456");
        var refreshHash = new TokenHash("refresh-hash-789");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);

        // Act
        await _store.RevokeAllUserSessionsThenCreateAsync(sessionId, userId, refreshHash, authIdentityId, expiresAt);

        // Assert
        var result = await _store.GetSessionAsync(sessionId);
        result.Should().NotBeNull();
        result!.SessionId.Should().Be(sessionId);
        result.UserId.Should().Be(userId);
        result.RefreshHash.Should().Be(refreshHash.HashBase64Url);
        result.AuthIdentityId.Should().Be(authIdentityId);
        result.ExpiresAtUtc.Should().Be(expiresAt);
        result.IsRevoked.Should().BeFalse();
        result.RevokedAtUtc.Should().BeNull();
        result.CreatedAtUtc.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact(DisplayName = "RevokeAllUserSessionsThenCreateAsync удаляет все предыдущие сессии пользователя")]
    public async Task RevokeAllUserSessionsThenCreateAsync_RemovesAllPreviousUserSessions()
    {
        // Arrange
        var userId = new UserId("user-456");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);

        // Создаем несколько сессий для одного пользователя
        await _store.RevokeAllUserSessionsThenCreateAsync("session-1", userId, new TokenHash("hash-1"), authIdentityId, expiresAt);
        await _store.RevokeAllUserSessionsThenCreateAsync("session-2", userId, new TokenHash("hash-2"), authIdentityId, expiresAt);
        await _store.RevokeAllUserSessionsThenCreateAsync("session-3", userId, new TokenHash("hash-3"), authIdentityId, expiresAt);

        // Act - создаем новую сессию, что должно удалить все предыдущие
        await _store.RevokeAllUserSessionsThenCreateAsync("new-session", userId, new TokenHash("new-hash"), authIdentityId, expiresAt);

        // Assert
        var session1 = await _store.GetSessionAsync("session-1");
        var session2 = await _store.GetSessionAsync("session-2");
        var session3 = await _store.GetSessionAsync("session-3");
        var newSession = await _store.GetSessionAsync("new-session");

        session1.Should().BeNull();
        session2.Should().BeNull();
        session3.Should().BeNull();
        newSession.Should().NotBeNull();
    }

    [Fact(DisplayName = "RevokeAllUserSessionsThenCreateAsync не затрагивает сессии других пользователей")]
    public async Task RevokeAllUserSessionsThenCreateAsync_DoesNotAffectOtherUsersSessions()
    {
        // Arrange
        var user1Id = new UserId("user-1");
        var user2Id = new UserId("user-2");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);

        // Создаем сессии для разных пользователей
        await _store.RevokeAllUserSessionsThenCreateAsync("user1-session", user1Id, new TokenHash("user1-hash"), authIdentityId, expiresAt);
        await _store.RevokeAllUserSessionsThenCreateAsync("user2-session", user2Id, new TokenHash("user2-hash"), authIdentityId, expiresAt);

        // Act - создаем новую сессию для первого пользователя
        await _store.RevokeAllUserSessionsThenCreateAsync("user1-new-session", user1Id, new TokenHash("user1-new-hash"), authIdentityId, expiresAt);

        // Assert
        var user1OldSession = await _store.GetSessionAsync("user1-session");
        var user1NewSession = await _store.GetSessionAsync("user1-new-session");
        var user2Session = await _store.GetSessionAsync("user2-session");

        user1OldSession.Should().BeNull(); // Старая сессия user1 должна быть удалена
        user1NewSession.Should().NotBeNull(); // Новая сессия user1 должна существовать
        user2Session.Should().NotBeNull(); // Сессия user2 не должна быть затронута
    }

    #endregion

    #region TryRotateRefreshAsync Tests

    [Fact(DisplayName = "TryRotateRefreshAsync с валидным хешем успешно обновляет refresh токен")]
    public async Task TryRotateRefreshAsync_WithValidHash_SuccessfullyUpdatesRefreshToken()
    {
        // Arrange
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");
        var oldRefreshHash = new TokenHash("old-refresh-hash");
        var newRefreshHash = new TokenHash("new-refresh-hash");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var oldExpiresAt = DateTime.UtcNow.AddHours(1);
        var newExpiresAt = DateTimeOffset.UtcNow.AddHours(2);

        await _store.RevokeAllUserSessionsThenCreateAsync(sessionId, userId, oldRefreshHash, authIdentityId, oldExpiresAt);

        // Act
        var result = await _store.TryRotateRefreshAsync(oldRefreshHash, newRefreshHash, newExpiresAt);

        // Assert
        result.Should().BeTrue();

        var updatedSession = await _store.GetSessionAsync(sessionId);
        updatedSession.Should().NotBeNull();
        updatedSession!.RefreshHash.Should().Be(newRefreshHash.HashBase64Url);
        updatedSession.ExpiresAtUtc.Should().Be(newExpiresAt.UtcDateTime);
    }

    [Fact(DisplayName = "TryRotateRefreshAsync с несуществующим хешем возвращает false")]
    public async Task TryRotateRefreshAsync_WithNonExistentHash_ReturnsFalse()
    {
        // Act
        var result = await _store.TryRotateRefreshAsync(
            new TokenHash("non-existent-hash"),
            new TokenHash("new-hash"),
            DateTimeOffset.UtcNow.AddHours(1));

        // Assert
        result.Should().BeFalse();
    }

    [Fact(DisplayName = "TryRotateRefreshAsync с истекшей сессией возвращает false и удаляет сессию")]
    public async Task TryRotateRefreshAsync_WithExpiredSession_ReturnsFalseAndRemovesSession()
    {
        // Arrange
        var sessionId = "expired-session-123";
        var userId = new UserId("user-456");
        var oldRefreshHash = new TokenHash("old-refresh-hash");
        var newRefreshHash = new TokenHash("new-refresh-hash");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiredTime = DateTime.UtcNow.AddHours(-1);

        await _store.RevokeAllUserSessionsThenCreateAsync(sessionId, userId, oldRefreshHash, authIdentityId, expiredTime);

        // Act
        var result = await _store.TryRotateRefreshAsync(oldRefreshHash, newRefreshHash, DateTimeOffset.UtcNow.AddHours(1));

        // Assert
        result.Should().BeFalse();

        // Проверяем, что сессия была удалена
        var session = await _store.GetSessionAsync(sessionId);
        session.Should().BeNull();
    }

    #endregion

    #region Integration and Edge Case Tests

    [Fact(DisplayName = "Множественные операции с одной сессией работают корректно")]
    public async Task MultipleOperationsOnSingleSession_WorkCorrectly()
    {
        // Arrange
        var sessionId = "test-session-123";
        var userId = new UserId("user-456");
        var initialRefreshHash = new TokenHash("initial-hash");
        var rotatedRefreshHash = new TokenHash("rotated-hash");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);

        // Act & Assert - создание сессии
        await _store.RevokeAllUserSessionsThenCreateAsync(sessionId, userId, initialRefreshHash, authIdentityId, expiresAt);
        var createdSession = await _store.GetSessionAsync(sessionId);
        createdSession.Should().NotBeNull();

        // Act & Assert - получение по refresh hash
        var sessionByHash = await _store.GetSessionByRefreshHashAsync(initialRefreshHash);
        sessionByHash.Should().NotBeNull();
        sessionByHash!.SessionId.Should().Be(sessionId);

        // Act & Assert - ротация refresh токена
        var rotateResult = await _store.TryRotateRefreshAsync(initialRefreshHash, rotatedRefreshHash, DateTimeOffset.UtcNow.AddHours(2));
        rotateResult.Should().BeTrue();

        // Act & Assert - проверка обновленной сессии
        var updatedSession = await _store.GetSessionByRefreshHashAsync(rotatedRefreshHash);
        updatedSession.Should().NotBeNull();
        updatedSession!.RefreshHash.Should().Be(rotatedRefreshHash.HashBase64Url);

        // Act & Assert - старый hash больше не работает
        var oldHashSession = await _store.GetSessionByRefreshHashAsync(initialRefreshHash);
        oldHashSession.Should().BeNull();
    }

    [Fact(DisplayName = "Обработка коллизии sessionId работает корректно")]
    public async Task SessionIdCollision_HandledCorrectly()
    {
        // Arrange
        var sessionId = "collision-session";
        var user1Id = new UserId("user-1");
        var user2Id = new UserId("user-2");
        var refreshHash1 = new TokenHash("hash-1");
        var refreshHash2 = new TokenHash("hash-2");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);

        // Act - создаем сессию для первого пользователя
        await _store.RevokeAllUserSessionsThenCreateAsync(sessionId, user1Id, refreshHash1, authIdentityId, expiresAt);
        var firstSession = await _store.GetSessionAsync(sessionId);
        firstSession.Should().NotBeNull();
        firstSession!.UserId.Should().Be(user1Id);

        // Act - создаем сессию с тем же sessionId для второго пользователя
        await _store.RevokeAllUserSessionsThenCreateAsync(sessionId, user2Id, refreshHash2, authIdentityId, expiresAt);
        var secondSession = await _store.GetSessionAsync(sessionId);

        // Assert - должна остаться только последняя сессия
        secondSession.Should().NotBeNull();
        secondSession!.UserId.Should().Be(user2Id);
        secondSession.RefreshHash.Should().Be(refreshHash2.HashBase64Url);
    }

    [Theory(DisplayName = "GetSessionAsync работает с различными форматами sessionId")]
    [InlineData("simple-session")]
    [InlineData("session-with-numbers-123")]
    [InlineData("550e8400-e29b-41d4-a716-446655440000")]
    [InlineData("session_with_underscores")]
    [InlineData("SESSION-WITH-CAPS")]
    public async Task GetSessionAsync_WorksWithVariousSessionIdFormats(string sessionId)
    {
        // Arrange
        var userId = new UserId("user-456");
        var refreshHash = new TokenHash("refresh-hash");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);

        await _store.RevokeAllUserSessionsThenCreateAsync(sessionId, userId, refreshHash, authIdentityId, expiresAt);

        // Act
        var result = await _store.GetSessionAsync(sessionId);

        // Assert
        result.Should().NotBeNull();
        result!.SessionId.Should().Be(sessionId);
    }

    [Fact(DisplayName = "Параллельные операции не вызывают исключений")]
    public async Task ConcurrentOperations_DoNotThrowExceptions()
    {
        // Arrange
        var tasks = new List<Task>();
        var userId = new UserId("concurrent-user");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);

        // Act - выполняем множественные параллельные операции
        for (int i = 0; i < 10; i++)
        {
            var sessionId = $"concurrent-session-{i}";
            var refreshHash = new TokenHash($"concurrent-hash-{i}");

            tasks.Add(_store.RevokeAllUserSessionsThenCreateAsync(sessionId, userId, refreshHash, authIdentityId, expiresAt));
            tasks.Add(_store.GetSessionAsync(sessionId));
            tasks.Add(_store.GetSessionByRefreshHashAsync(refreshHash));
        }

        // Assert - все операции должны завершиться без исключений
        var exception = await Record.ExceptionAsync(() => Task.WhenAll(tasks));
        exception.Should().BeNull();
    }

    [Fact(DisplayName = "Время создания сессии устанавливается корректно")]
    public async Task SessionCreationTime_SetCorrectly()
    {
        // Arrange
        var sessionId = "time-test-session";
        var userId = new UserId("user-456");
        var refreshHash = new TokenHash("refresh-hash");
        var authIdentityId = new AuthIdentityId("auth-identity-101");
        var expiresAt = DateTime.UtcNow.AddHours(1);
        var beforeCreation = DateTime.UtcNow;

        // Act
        await _store.RevokeAllUserSessionsThenCreateAsync(sessionId, userId, refreshHash, authIdentityId, expiresAt);
        var afterCreation = DateTime.UtcNow;

        // Assert
        var session = await _store.GetSessionAsync(sessionId);
        session.Should().NotBeNull();
        session!.CreatedAtUtc.Should().BeOnOrAfter(beforeCreation);
        session.CreatedAtUtc.Should().BeOnOrBefore(afterCreation);
    }

    [Fact(DisplayName = "Пустое хранилище корректно обрабатывает все операции")]
    public async Task EmptyStore_HandlesAllOperationsCorrectly()
    {
        // Act & Assert
        var getSessionResult = await _store.GetSessionAsync("non-existent");
        getSessionResult.Should().BeNull();

        var getByHashResult = await _store.GetSessionByRefreshHashAsync(new TokenHash("non-existent"));
        getByHashResult.Should().BeNull();

        var rotateResult = await _store.TryRotateRefreshAsync(
            new TokenHash("old"),
            new TokenHash("new"),
            DateTimeOffset.UtcNow.AddHours(1));
        rotateResult.Should().BeFalse();

        // Создание сессии в пустом хранилище должно работать
        var createAction = () => _store.RevokeAllUserSessionsThenCreateAsync(
            "new-session",
            new UserId("user"),
            new TokenHash("hash"),
            new AuthIdentityId("auth"),
            DateTime.UtcNow.AddHours(1));

        await createAction.Should().NotThrowAsync();
    }

    #endregion
}
