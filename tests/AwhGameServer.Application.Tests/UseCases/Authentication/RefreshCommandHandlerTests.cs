using AwhGameServer.Application.Abstractions.Models;
using AwhGameServer.Application.Abstractions.Repositories;
using AwhGameServer.Application.Abstractions.Services;
using AwhGameServer.Application.Abstractions.Stores;
using AwhGameServer.Application.Abstractions.UnitOfWork;
using AwhGameServer.Application.UseCases.Authentication;
using AwhGameServer.Domain.Aggregates.Game;
using AwhGameServer.Domain.Aggregates.Users;
using AwhGameServer.Domain.Entities.Game;
using AwhGameServer.Domain.ValueObjects.Users;
using Moq;

namespace AwhGameServer.Application.Tests.UseCases.Authentication;

public class RefreshCommandHandlerTests
{
    private readonly Mock<IAuthenticationUow> _uowMock;
    private readonly Mock<IAuthTokensGenerator> _authTokensGeneratorMock;
    private readonly Mock<ITokenHasher> _tokenHasherMock;
    private readonly Mock<IAuthSessionStore> _authSessionStoreMock;
    private readonly Mock<IAuthMethodsConfigReadRepository> _authMethodsConfigRepoMock;
    private readonly Mock<IAuthIdentityRepository> _authIdentityRepoMock;
    private readonly RefreshCommandHandler _handler;

    public RefreshCommandHandlerTests()
    {
        _uowMock = new Mock<IAuthenticationUow>();
        _authTokensGeneratorMock = new Mock<IAuthTokensGenerator>();
        _tokenHasherMock = new Mock<ITokenHasher>();
        _authSessionStoreMock = new Mock<IAuthSessionStore>();
        _authMethodsConfigRepoMock = new Mock<IAuthMethodsConfigReadRepository>();
        _authIdentityRepoMock = new Mock<IAuthIdentityRepository>();

        _uowMock.Setup(x => x.AuthMethodsConfigRepository).Returns(_authMethodsConfigRepoMock.Object);
        _uowMock.Setup(x => x.AuthIdentityRepository).Returns(_authIdentityRepoMock.Object);

        _handler = new RefreshCommandHandler(
            _uowMock.Object,
            _authTokensGeneratorMock.Object,
            _tokenHasherMock.Object,
            _authSessionStoreMock.Object);
    }

    #region Successful Scenarios

    [Fact(DisplayName = "Успешное обновление токенов при валидном refresh token")]
    public async Task Handle_WithValidRefreshToken_ReturnsNewTokens()
    {
        // Arrange
        const string refreshToken = "valid-refresh-token";
        var refreshTokenHash = new TokenHash("hashed-refresh-token");
        var newRefreshTokenHash = new TokenHash("new-hashed-refresh-token");
        
        var session = CreateValidSession();
        var authIdentity = CreateAuthIdentity();
        var authMethodsConfig = CreateAuthMethodsConfig(("OAuth2.0_Google", true, true));
        var newAuthTokens = CreateAuthTokens();

        SetupMocks(refreshToken, refreshTokenHash, newRefreshTokenHash, session, authIdentity, authMethodsConfig, newAuthTokens);

        var command = new RefreshCommand(refreshToken);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.Equal(newAuthTokens.AccessToken, result.AccessToken);
        Assert.Equal(newAuthTokens.RefreshToken, result.RefreshToken);

        VerifySuccessfulRefreshFlow(refreshToken, refreshTokenHash, newRefreshTokenHash, session, authIdentity, newAuthTokens);
    }

    #endregion

    #region Input Validation Tests

    [Theory(DisplayName = "Выбрасывает ArgumentException при пустом или null refresh token")]
    [InlineData("")]
    [InlineData("   ")]
    public async Task Handle_WithInvalidRefreshToken_ThrowsArgumentException(string invalidRefreshToken)
    {
        // Arrange
        var command = new RefreshCommand(invalidRefreshToken);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(() =>
            _handler.Handle(command, CancellationToken.None));

        Assert.StartsWith("Refresh token cannot be null or empty", exception.Message);
        Assert.Equal("RefreshToken", exception.ParamName);
    }

    [Fact(DisplayName = "Выбрасывает ArgumentException при null refresh token")]
    public async Task Handle_WithNullRefreshToken_ThrowsArgumentException()
    {
        // Arrange
        var command = new RefreshCommand(null!);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(() =>
            _handler.Handle(command, CancellationToken.None));

        Assert.StartsWith("Refresh token cannot be null or empty", exception.Message);
        Assert.Equal("RefreshToken", exception.ParamName);
    }

    #endregion

    #region Session Validation Tests

    [Fact(DisplayName = "Выбрасывает ArgumentException когда сессия не найдена")]
    public async Task Handle_WithNonExistentSession_ThrowsArgumentException()
    {
        // Arrange
        const string refreshToken = "non-existent-refresh-token";
        var refreshTokenHash = new TokenHash("hashed-refresh-token");

        _tokenHasherMock.Setup(x => x.HashToken(refreshToken, It.IsAny<CancellationToken>()))
            .ReturnsAsync(refreshTokenHash);
        
        _authSessionStoreMock.Setup(x => x.GetSessionByRefreshHashAsync(refreshTokenHash, It.IsAny<CancellationToken>()))
            .ReturnsAsync((AuthSession?)null);

        var command = new RefreshCommand(refreshToken);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(() =>
            _handler.Handle(command, CancellationToken.None));

        Assert.StartsWith("Refresh token is invalid or expired", exception.Message);
        Assert.Equal("RefreshToken", exception.ParamName);
    }

    [Fact(DisplayName = "Выбрасывает ArgumentException когда сессия отозвана")]
    public async Task Handle_WithRevokedSession_ThrowsArgumentException()
    {
        // Arrange
        const string refreshToken = "revoked-refresh-token";
        var refreshTokenHash = new TokenHash("hashed-refresh-token");
        var revokedSession = CreateRevokedSession();

        _tokenHasherMock.Setup(x => x.HashToken(refreshToken, It.IsAny<CancellationToken>()))
            .ReturnsAsync(refreshTokenHash);
        
        _authSessionStoreMock.Setup(x => x.GetSessionByRefreshHashAsync(refreshTokenHash, It.IsAny<CancellationToken>()))
            .ReturnsAsync(revokedSession);

        var command = new RefreshCommand(refreshToken);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(() =>
            _handler.Handle(command, CancellationToken.None));

        Assert.StartsWith("Refresh token is revoked", exception.Message);
        Assert.Equal("RefreshToken", exception.ParamName);
    }

    [Fact(DisplayName = "Выбрасывает ArgumentException когда сессия истекла")]
    public async Task Handle_WithExpiredSession_ThrowsArgumentException()
    {
        // Arrange
        const string refreshToken = "expired-refresh-token";
        var refreshTokenHash = new TokenHash("hashed-refresh-token");
        var expiredSession = CreateExpiredSession();

        _tokenHasherMock.Setup(x => x.HashToken(refreshToken, It.IsAny<CancellationToken>()))
            .ReturnsAsync(refreshTokenHash);
        
        _authSessionStoreMock.Setup(x => x.GetSessionByRefreshHashAsync(refreshTokenHash, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expiredSession);

        var command = new RefreshCommand(refreshToken);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(() =>
            _handler.Handle(command, CancellationToken.None));

        Assert.StartsWith("Refresh token is expired", exception.Message);
        Assert.Equal("RefreshToken", exception.ParamName);
    }

    #endregion

    #region AuthIdentity Validation Tests

    [Fact(DisplayName = "Выбрасывает ArgumentException когда AuthIdentity не найден")]
    public async Task Handle_WithNonExistentAuthIdentity_ThrowsArgumentException()
    {
        // Arrange
        const string refreshToken = "valid-refresh-token";
        var refreshTokenHash = new TokenHash("hashed-refresh-token");
        var session = CreateValidSession();

        _tokenHasherMock.Setup(x => x.HashToken(refreshToken, It.IsAny<CancellationToken>()))
            .ReturnsAsync(refreshTokenHash);
        
        _authSessionStoreMock.Setup(x => x.GetSessionByRefreshHashAsync(refreshTokenHash, It.IsAny<CancellationToken>()))
            .ReturnsAsync(session);
        
        _authIdentityRepoMock.Setup(x => x.GetByIdAsync(session.AuthIdentityId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((AuthIdentity?)null);

        var command = new RefreshCommand(refreshToken);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(() =>
            _handler.Handle(command, CancellationToken.None));

        Assert.StartsWith("Auth identity not found", exception.Message);
        Assert.Equal("RefreshToken", exception.ParamName);
    }

    [Fact(DisplayName = "Выбрасывает ArgumentException когда метод аутентификации не разрешен")]
    public async Task Handle_WithDisallowedAuthMethod_ThrowsArgumentException()
    {
        // Arrange
        const string refreshToken = "valid-refresh-token";
        var refreshTokenHash = new TokenHash("hashed-refresh-token");
        var session = CreateValidSession();
        var authIdentity = CreateAuthIdentity();
        var authMethodsConfig = CreateAuthMethodsConfig(("OAuth2.0_Google", false, false));

        _tokenHasherMock.Setup(x => x.HashToken(refreshToken, It.IsAny<CancellationToken>()))
            .ReturnsAsync(refreshTokenHash);
        
        _authSessionStoreMock.Setup(x => x.GetSessionByRefreshHashAsync(refreshTokenHash, It.IsAny<CancellationToken>()))
            .ReturnsAsync(session);
        
        _authIdentityRepoMock.Setup(x => x.GetByIdAsync(session.AuthIdentityId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(authIdentity);
        
        _authMethodsConfigRepoMock.Setup(x => x.GetConfigAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(authMethodsConfig);

        var command = new RefreshCommand(refreshToken);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(() =>
            _handler.Handle(command, CancellationToken.None));

        Assert.StartsWith("Login with OAuth2.0_Google is not allowed", exception.Message);
        Assert.Equal("RefreshToken", exception.ParamName);
    }

    #endregion

    #region Helper Methods

    private static AuthSession CreateValidSession()
    {
        return new AuthSession(
            "session-123",
            new UserId("user-123"),
            "refresh-hash",
            new AuthIdentityId("auth-identity-123"),
            false,
            null,
            DateTime.UtcNow.AddHours(-1),
            DateTime.UtcNow.AddDays(30));
    }

    private static AuthSession CreateRevokedSession()
    {
        return new AuthSession(
            "session-123",
            new UserId("user-123"),
            "refresh-hash",
            new AuthIdentityId("auth-identity-123"),
            true,
            DateTime.UtcNow.AddMinutes(-10),
            DateTime.UtcNow.AddHours(-1),
            DateTime.UtcNow.AddDays(30));
    }

    private static AuthSession CreateExpiredSession()
    {
        return new AuthSession(
            "session-123",
            new UserId("user-123"),
            "refresh-hash",
            new AuthIdentityId("auth-identity-123"),
            false,
            null,
            DateTime.UtcNow.AddDays(-31),
            DateTime.UtcNow.AddDays(-1));
    }

    private static AuthIdentity CreateAuthIdentity()
    {
        return new AuthIdentity(
            new AuthIdentityId("auth-identity-123"),
            new UserId("user-123"),
            "auth-token-456",
            "OAuth2.0_Google");
    }

    private static AuthMethodsConfig CreateAuthMethodsConfig(params (string methodKey, bool isRegistrationAllowed, bool isLoginAllowed)[] methods)
    {
        var authMethods = methods.Select(m => new AuthMethod(m.methodKey, m.isRegistrationAllowed, m.isLoginAllowed));
        return new AuthMethodsConfig(authMethods);
    }

    private static AuthTokens CreateAuthTokens()
    {
        return new AuthTokens(
            "new-access-token",
            "new-refresh-token",
            DateTime.UtcNow.AddMinutes(15),
            DateTime.UtcNow.AddDays(30));
    }

    private void SetupMocks(
        string refreshToken,
        TokenHash refreshTokenHash,
        TokenHash newRefreshTokenHash,
        AuthSession session,
        AuthIdentity authIdentity,
        AuthMethodsConfig authMethodsConfig,
        AuthTokens newAuthTokens)
    {
        _tokenHasherMock.Setup(x => x.HashToken(refreshToken, It.IsAny<CancellationToken>()))
            .ReturnsAsync(refreshTokenHash);
        
        _tokenHasherMock.Setup(x => x.HashToken(newAuthTokens.RefreshToken, It.IsAny<CancellationToken>()))
            .ReturnsAsync(newRefreshTokenHash);
        
        _authSessionStoreMock.Setup(x => x.GetSessionByRefreshHashAsync(refreshTokenHash, It.IsAny<CancellationToken>()))
            .ReturnsAsync(session);
        
        _authIdentityRepoMock.Setup(x => x.GetByIdAsync(session.AuthIdentityId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(authIdentity);
        
        _authMethodsConfigRepoMock.Setup(x => x.GetConfigAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(authMethodsConfig);
        
        _authTokensGeneratorMock.Setup(x => x.GenerateAuthTokens(session.SessionId, authIdentity.UserId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(newAuthTokens);
    }

    private void VerifySuccessfulRefreshFlow(
        string refreshToken,
        TokenHash refreshTokenHash,
        TokenHash newRefreshTokenHash,
        AuthSession session,
        AuthIdentity authIdentity,
        AuthTokens newAuthTokens)
    {
        _tokenHasherMock.Verify(x => x.HashToken(refreshToken, It.IsAny<CancellationToken>()), Times.Once);
        _tokenHasherMock.Verify(x => x.HashToken(newAuthTokens.RefreshToken, It.IsAny<CancellationToken>()), Times.Once);
        
        _authSessionStoreMock.Verify(x => x.GetSessionByRefreshHashAsync(refreshTokenHash, It.IsAny<CancellationToken>()), Times.Once);
        
        _authIdentityRepoMock.Verify(x => x.GetByIdAsync(session.AuthIdentityId, It.IsAny<CancellationToken>()), Times.Once);
        
        _authMethodsConfigRepoMock.Verify(x => x.GetConfigAsync(It.IsAny<CancellationToken>()), Times.Once);
        
        _authTokensGeneratorMock.Verify(x => x.GenerateAuthTokens(session.SessionId, authIdentity.UserId, It.IsAny<CancellationToken>()), Times.Once);
        
        _authSessionStoreMock.Verify(x => x.TryRotateRefreshAsync(
            refreshTokenHash,
            newRefreshTokenHash,
            newAuthTokens.RefreshTokenExpiresAtUtc,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion
}
